/*
 * Yendor Cats - Carousel Component
 * Reusable carousel for image galleries and cat showcases
 */

.carousel {
    position: relative;
    width: 100%;
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
}

.carousel-container {
    display: flex;
    transition: transform 0.5s ease;
    width: 100%;
}

/* Force each slide to take full width and prevent overlap */
.carousel-slide {
    min-width: 100%;
    width: 100%;
    flex: 0 0 100%;
    position: relative;
    box-sizing: border-box;
}

.carousel-slide img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block;
    cursor: pointer;
}

/* Multiple items per slide view */
.carousel.multi-view .carousel-slide {
    min-width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
}

.carousel.multi-view .carousel-item {
    flex: 0 0 calc(33.333% - var(--spacing-md));
    max-width: calc(33.333% - var(--spacing-md));
    height: 350px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    position: relative;
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.carousel.multi-view .carousel-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.carousel.multi-view .carousel-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    cursor: pointer; /* Indicate clickable for lightbox */
}

.carousel-item-content {
    padding: var(--spacing-sm);
    background-color: var(--bg-primary);
}

.carousel-item-content h3 {
    margin-bottom: var(--spacing-xs);
    font-size: 1.1rem;
}

.carousel-item-content p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.carousel-item-link {
    display: block;
    text-align: right;
    color: var(--accent-primary);
    font-size: 0.9rem;
    font-weight: 600;
}

/* Navigation Controls */
.carousel-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
    z-index: 10;
}

/* Make navigation buttons MUCH more visible */
.carousel-button {
    background-color: rgba(0, 0, 0, 0.7);
    border: var(--color-gold);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    opacity: 0.8;
}

.carousel-button:hover {
    opacity: 1;
    background-color: var(--accent-primary);
    transform: translateY(-50%) scale(1.1);
}

.carousel-button.prev {
    left: 15px;
}

.carousel-button.next {
    right: 15px;
}

.carousel-button svg {
    width: 24px;
    height: 24px;
    stroke: var(--color-black);
    stroke-width: 3;
}

/* Indicators/Dots */
.carousel-indicators {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 8px;
    z-index: 10;
    padding: 10px 0;
}

.carousel-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--color-gold);
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin: 0 5px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.carousel-indicator.active {
    background-color: var(--accent-primary);
    transform: scale(1.2);
}

/* Autoplay Progress */
.carousel-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: var(--accent-primary);
    z-index: 11;
    transition: width 0.1s linear;
}

/* Image Lightbox/Popup */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-image {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    border: 5px solid var(--bg-primary);
    border-radius: var(--border-radius-sm);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    z-index: var(--z-modal);
}

.lightbox-close:hover {
    background-color: rgba(255, 255, 255, 0.4);
}

.lightbox-close svg {
    width: 24px;
    height: 24px;
    stroke: white;
}

.lightbox-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: calc(var(--z-modal) - 1);
}

.lightbox-nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    pointer-events: auto;
}

.lightbox-nav-button:hover {
    background-color: var(--bg-secondary);
}

.lightbox-nav-button.prev {
    left: 20px;
}

.lightbox-nav-button.next {
    right: 20px;
}

.lightbox-nav-button svg {
    width: 24px;
    height: 24px;
    stroke: white;
}

/* Responsive adjustments */
@media (min-width: 768px) {
    .carousel-slide img {
        height: 500px;
    }

    .carousel.multi-view .carousel-item {
        flex: 0 0 calc(33.333% - var(--spacing-md));
        max-width: calc(33.333% - var(--spacing-md));
    }

    .carousel-button {
        width: 50px;
        height: 50px;
    }

    .carousel-button svg {
        width: 24px;
        height: 24px;
    }
}

@media (max-width: 767px) {
    .carousel.multi-view .carousel-item {
        flex: 0 0 calc(100% - var(--spacing-md));
        max-width: calc(100% - var(--spacing-md));
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .carousel.multi-view .carousel-item {
        flex: 0 0 calc(50% - var(--spacing-md));
        max-width: calc(50% - var(--spacing-md));
    }
}

@media (min-width: 1200px) {
    .carousel-slide img {
        height: 600px;
    }
}

/* Slide info overlay for cat metadata */

.slide-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 20px;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.carousel-slide .slide-info h3 {
    margin: 0 0 5px 0;
    font-size: 1.8rem;
    text-transform: capitalize;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.carousel-slide .slide-info p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* Loading indicator */

.carousel-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 250px;
    width: 100%;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-primary);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: var(--spacing-sm);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Empty and error states */

.carousel-empty,
.carousel-error,
.empty-message,
.error-message {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    padding: var(--spacing-md);
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-md);
    color: var(--text-secondary);
    text-align: center;
    width: 100%;
}

.error-message {
    color: var(--color-error, #d32f2f);
}

/* Slide Counter */

.carousel-slide-counter {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: var(--border-radius-sm);
    padding: 5px 10px;
    font-size: 1rem;
    font-weight: bold;
    z-index: 10;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.carousel-slide-counter span:first-child {
    color: var(--accent-primary);
}

/* Loading Placeholder */

.carousel-slide.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: var(--bg-secondary);
    min-height: 400px;
}

.carousel-slide.loading-placeholder .spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top-color: var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Gallery Lightbox Styles */

.gallery-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.gallery-lightbox.active {
    opacity: 1;
    visibility: visible;
}

.gallery-lightbox .lightbox-content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 90%;
    max-height: 90vh;
}

.gallery-lightbox .lightbox-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border: 3px solid var(--accent-primary);
    border-radius: var(--border-radius-sm);
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
}

.gallery-lightbox .lightbox-caption {
    margin-top: 15px;
    padding: 10px 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: var(--border-radius-sm);
    text-align: center;
    max-width: 80%;
}

.gallery-lightbox .lightbox-close {
    position: absolute;
    top: -40px;
    right: -40px;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    z-index: 10000;
}

.gallery-lightbox .lightbox-close:hover {
    background-color: var(--accent-primary);
}

.gallery-lightbox .lightbox-close svg {
    width: 24px;
    height: 24px;
    stroke: white;
    stroke-width: 3;
}

.gallery-lightbox .lightbox-nav {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.gallery-lightbox .lightbox-nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    pointer-events: auto;
}

.gallery-lightbox .lightbox-nav-button:hover {
    background-color: var(--accent-primary);
    transform: translateY(-50%) scale(1.1);
}

.gallery-lightbox .lightbox-nav-button.prev {
    left: 20px;
}

.gallery-lightbox .lightbox-nav-button.next {
    right: 20px;
}

.gallery-lightbox .lightbox-nav-button svg {
    width: 30px;
    height: 30px;
    stroke: white;
    stroke-width: 3;
}

/* Cat Cards */

.cat-card {
    background-color: var(--color-black);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin: 0 10px;
    width: 300px;
    flex: 0 0 auto;
}

.cat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cat-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.cat-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    cursor: pointer;
}

.cat-image::after {
    content: '🔍 Click to enlarge';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px;
    text-align: center;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.cat-card:hover .cat-image img {
    transform: scale(1.05);
}

.cat-card:hover .cat-image::after {
    transform: translateY(0);
}

.cat-info {
    padding: 15px;
}

.cat-info h3 {
    margin: 0 0 10px 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.cat-info p {
    margin: 5px 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Thumbnail Gallery Popup */

.thumbnail-gallery-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    z-index: 9998;
    display: flex;
    flex-direction: column;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    overflow: hidden;
}

.thumbnail-gallery-popup.active {
    opacity: 1;
    visibility: visible;
}

.thumbnail-gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: rgba(0, 0, 0, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.thumbnail-gallery-title {
    color: white;
    font-size: 1.5rem;
    margin: 0;
}

.thumbnail-gallery-close {
    background-color: transparent;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.thumbnail-gallery-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.thumbnail-gallery-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.thumbnail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    max-width: 1400px;
    margin: 0 auto;
}

.thumbnail-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    aspect-ratio: 1 / 1;
}

.thumbnail-item:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.thumbnail-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 10px;
    color: white;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.thumbnail-item:hover .thumbnail-info {
    transform: translateY(0);
}

.thumbnail-info h4 {
    margin: 0 0 5px 0;
    font-size: 1rem;
}

.thumbnail-info p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.8;
}

/* View All Button */

.view-all-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    background-color: var(--accent-primary);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.view-all-button:hover {
    background-color: var(--accent-secondary, var(--accent-dark, #004d40));
    transform: translateY(-2px);
}

.view-all-button svg {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}

/* Media Queries for Thumbnail Grid */
@media (max-width: 768px) {
    .thumbnail-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }

    .thumbnail-gallery-title {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .thumbnail-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 8px;
    }

    .thumbnail-gallery-header {
        padding: 10px 15px;
    }
}

/* Thumbnail Gallery Loading Placeholder */

.thumbnail-grid .loading-placeholder,
.thumbnail-grid .empty-message,
.thumbnail-grid .error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    width: 100%;
    text-align: center;
    padding: 30px;
    color: white;
    grid-column: 1 / -1;
}

.thumbnail-grid .loading-placeholder .spinner {
    width: 60px;
    height: 60px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top-color: var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
}

/* Section Header with View All button */

.section-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    margin-bottom: 0.5rem;
}

.section-header p {
    margin-top: 0.5rem;
    margin-bottom: 1rem;
}

.section-header .view-all-button {
    margin-top: 0.5rem;
}

/* Sort Buttons */

.sort-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 10px;
}

.sort-buttons .view-all-button {
    margin-left: 15px;
}

/* Ensure View All button is visible and well-positioned in both contexts */

.view-all-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    background-color: var(--accent-primary);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

/* Cat description in lightbox */

.cat-description {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    font-style: italic;
    line-height: 1.5;
    max-height: 120px;
    overflow-y: auto;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.9);
}

/* Thumbnail description */

.thumbnail-description {
    font-size: 0.75rem;
    opacity: 0.9;
    margin-top: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}